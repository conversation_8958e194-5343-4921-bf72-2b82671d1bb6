using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using ReactApp.Server.Authentication.Interfaces;
using System.IdentityModel.Tokens.Jwt;

namespace ReactApp.Server.Authentication.Services
{
    public class JwtTokenValidator : ITokenValidator
    {
        private readonly IConfiguration _configuration;

        public JwtTokenValidator(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var configManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                    $"{_configuration["AzureAd:Instance"]}{_configuration["AzureAd:TenantId"]}/.well-known/openid-configuration",
                    new OpenIdConnectConfigurationRetriever());

                var config = await configManager.GetConfigurationAsync();
                var tokenHandler = new JwtSecurityTokenHandler();

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = $"{_configuration["AzureAd:Instance"]}{_configuration["AzureAd:TenantId"]}/v2.0",
                    ValidateAudience = true,
                    ValidAudience = _configuration["AzureAd:ClientId"],
                    ValidateLifetime = true,
                    IssuerSigningKeys = config.SigningKeys
                };

                tokenHandler.ValidateToken(token, validationParameters, out _);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public Task<Dictionary<string, string>> GetClaimsFromTokenAsync(string token)
        {
            var claims = new Dictionary<string, string>();

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(token);

                foreach (var claim in jwtToken.Claims)
                {
                    if (!claims.ContainsKey(claim.Type))
                    {
                        claims.Add(claim.Type, claim.Value);
                    }
                }
            }
            catch
            {
                // Return empty claims if token parsing fails
            }

            return Task.FromResult(claims);
        }
    }
}