using Microsoft.AspNetCore.Mvc;
using ReactApp.Server.Authentication.Interfaces;
using ReactApp.Server.Authentication.Models;

namespace ReactApp.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthenticationService _authService;
        private readonly IUserService _userService;
        private readonly IConfiguration _configuration;

        public AuthController(
            IAuthenticationService authService,
            IUserService userService,
            IConfiguration configuration)
        {
            _authService = authService;
            _userService = userService;
            _configuration = configuration;
        }

        [HttpGet("login")]
        public IActionResult Login()
        {
            var redirectUri = $"{Request.Scheme}://{Request.Host}/api/auth/callback";
            var loginUrl = _authService.GetLoginUrl(redirectUri);
            return Ok(new { loginUrl });
        }

        [HttpGet("callback")]
        public IActionResult Callback([FromQuery] string code)
        {
            // In a real implementation, you would exchange the code for tokens
            // For simplicity, we're redirecting to the frontend with the code
            var frontendUrl = _configuration["SpaRedirectUri"] ?? "https://localhost:51049";
            return Redirect($"{frontendUrl}?code={code}");
        }

        [HttpPost("validate")]
        public async Task<IActionResult> ValidateToken([FromBody] TokenValidationRequest request)
        {
            var result = await _authService.ValidateTokenAsync(request.Token);
            return Ok(result);
        }

        [HttpGet("user")]
        public async Task<IActionResult> GetUserInfo()
        {
            string authHeader = Request.Headers["Authorization"];
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized();
            }

            string token = authHeader.Substring("Bearer ".Length);
            var validationResult = await _authService.ValidateTokenAsync(token);
            
            if (!validationResult.IsAuthenticated || string.IsNullOrEmpty(validationResult.UserId))
            {
                return Unauthorized();
            }

            var userInfo = await _userService.GetUserInfoAsync(validationResult.UserId);
            return Ok(userInfo);
        }
    }

    public class TokenValidationRequest
    {
        public string Token { get; set; } = string.Empty;
    }
}