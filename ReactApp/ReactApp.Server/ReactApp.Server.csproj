<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\reactapp.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:51049</SpaProxyServerUrl>
  </PropertyGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net9.0'">
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>9.*-*</Version>
    </PackageReference>
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net9.0'">
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.3" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.9.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\reactapp.client\reactapp.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

</Project>
